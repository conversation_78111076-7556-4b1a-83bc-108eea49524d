'use client'

import { useState } from 'react'
import { Video, Play, Calendar, ExternalLink } from 'lucide-react'

// 模拟数据 - 实际使用时会从Supabase获取
const mockVideos = [
  {
    id: '1',
    title: '《夜空中最亮的星》创作过程分享',
    description: '分享这首歌的创作灵感和制作过程，从词曲创作到录音制作的完整流程。',
    bilibili_url: 'https://www.bilibili.com/video/BV1234567890',
    bilibili_embed_id: 'BV1234567890',
    thumbnail_url: '/thumbnails/video1.jpg',
    created_at: '2024-01-15',
  },
  {
    id: '2',
    title: '吉他弹唱《时光倒流》',
    description: '用吉他弹唱这首关于回忆的歌曲，希望能唤起大家心中美好的回忆。',
    bilibili_url: 'https://www.bilibili.com/video/BV2345678901',
    bilibili_embed_id: 'BV2345678901',
    thumbnail_url: '/thumbnails/video2.jpg',
    created_at: '2024-01-10',
  },
  {
    id: '3',
    title: '音乐制作软件使用教程',
    description: '分享我常用的音乐制作软件和技巧，适合音乐制作初学者观看学习。',
    bilibili_url: 'https://www.bilibili.com/video/BV3456789012',
    bilibili_embed_id: 'BV3456789012',
    thumbnail_url: '/thumbnails/video3.jpg',
    created_at: '2024-01-05',
  },
]

export default function VideoPage() {
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null)

  const getBilibiliEmbedUrl = (embedId: string) => {
    return `//player.bilibili.com/player.html?bvid=${embedId}&page=1&high_quality=1&danmaku=0`
  }

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <Video className="text-red-400" size={48} />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-wider">VIDEOS</h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Watch my video content on Bilibili, including music creation sharing, performance videos and tutorials
          </p>
        </div>

        {/* Video Player Section */}
        {selectedVideo && (
          <div className="mb-12">
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="video-container">
                <iframe
                  src={getBilibiliEmbedUrl(selectedVideo)}
                  scrolling="no"
                  border="0"
                  frameBorder="no"
                  allowFullScreen
                  title="Bilibili Video Player"
                ></iframe>
              </div>
            </div>
          </div>
        )}

        {/* Video Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {mockVideos.map((video) => (
            <div
              key={video.id}
              className="bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-colors duration-200"
            >
              {/* Thumbnail */}
              <div className="relative aspect-video bg-gray-600 flex items-center justify-center">
                <div className="absolute inset-0 bg-black/20"></div>
                <Video className="text-gray-400 z-10" size={48} />
                <button
                  onClick={() => setSelectedVideo(video.bilibili_embed_id)}
                  className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 hover:opacity-100 transition-opacity duration-200 z-20"
                >
                  <div className="w-16 h-16 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center">
                    <Play className="text-white ml-1" size={24} />
                  </div>
                </button>
              </div>

              {/* Video Info */}
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3 line-clamp-2">
                  {video.title}
                </h3>
                <p className="text-gray-400 text-sm mb-4 line-clamp-3">
                  {video.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="mr-1" size={16} />
                    <span>{video.created_at}</span>
                  </div>
                  
                  <a
                    href={video.bilibili_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-red-400 hover:text-red-300 text-sm font-medium"
                  >
                    <ExternalLink className="mr-1" size={16} />
                    在B站观看
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {mockVideos.length === 0 && (
          <div className="text-center py-20">
            <Video className="mx-auto text-gray-600 mb-4" size={64} />
            <h3 className="text-2xl font-semibold text-gray-400 mb-2">
              暂无视频作品
            </h3>
            <p className="text-gray-500">
              敬请期待更多精彩的视频内容
            </p>
          </div>
        )}

        {/* B站链接 */}
        <div className="mt-16 text-center">
          <div className="bg-gray-800 rounded-lg p-8">
            <h3 className="text-2xl font-semibold mb-4">关注我的B站</h3>
            <p className="text-gray-400 mb-6">
              在B站关注我，第一时间观看最新的音乐和视频内容
            </p>
            <a
              href="https://space.bilibili.com/your_uid"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-lg transition-colors duration-200"
            >
              <ExternalLink className="mr-2" size={20} />
              访问我的B站主页
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
