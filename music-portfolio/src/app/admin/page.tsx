'use client'

import { useState, useEffect } from 'react'
import { Settings, Music, Video, User, Plus, Edit, Trash2 } from 'lucide-react'

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState('music')
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 检查认证状态
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/admin/auth', {
        credentials: 'include'
      })
      setIsAuthenticated(response.ok)
    } catch (error) {
      setIsAuthenticated(false)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogin = () => {
    const username = prompt('请输入用户名:')
    const password = prompt('请输入密码:')
    
    if (username && password) {
      const credentials = btoa(`${username}:${password}`)
      fetch('/api/admin/auth', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      }).then(response => {
        if (response.ok) {
          setIsAuthenticated(true)
        } else {
          alert('认证失败，请检查用户名和密码')
        }
      })
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-gray-400">加载中...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-gray-800 p-8 rounded-lg max-w-md w-full mx-4">
          <div className="text-center mb-8">
            <Settings className="mx-auto text-blue-400 mb-4" size={48} />
            <h1 className="text-2xl font-bold mb-2">后台管理系统</h1>
            <p className="text-gray-400">请登录以访问管理功能</p>
          </div>
          
          <button
            onClick={handleLogin}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
          >
            登录
          </button>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'music', label: '音乐管理', icon: Music },
    { id: 'video', label: '视频管理', icon: Video },
    { id: 'profile', label: '个人信息', icon: User },
  ]

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">后台管理系统</h1>
          <p className="text-gray-400">管理您的音乐和视频内容</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-700 mb-8">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  <Icon className="mr-2" size={20} />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="bg-gray-800 rounded-lg p-6">
          {activeTab === 'music' && <MusicManagement />}
          {activeTab === 'video' && <VideoManagement />}
          {activeTab === 'profile' && <ProfileManagement />}
        </div>
      </div>
    </div>
  )
}

function MusicManagement() {
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">音乐作品管理</h2>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
          <Plus className="mr-2" size={20} />
          添加音乐
        </button>
      </div>
      
      <div className="space-y-4">
        <div className="bg-gray-700 p-4 rounded-lg flex items-center justify-between">
          <div>
            <h3 className="font-semibold">夜空中最亮的星</h3>
            <p className="text-gray-400 text-sm">演唱：马君 | 时长：4:32</p>
          </div>
          <div className="flex space-x-2">
            <button className="text-blue-400 hover:text-blue-300 p-2">
              <Edit size={20} />
            </button>
            <button className="text-red-400 hover:text-red-300 p-2">
              <Trash2 size={20} />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

function VideoManagement() {
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">视频作品管理</h2>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
          <Plus className="mr-2" size={20} />
          添加视频
        </button>
      </div>
      
      <div className="space-y-4">
        <div className="bg-gray-700 p-4 rounded-lg flex items-center justify-between">
          <div>
            <h3 className="font-semibold">《夜空中最亮的星》创作过程分享</h3>
            <p className="text-gray-400 text-sm">发布时间：2024-01-15</p>
          </div>
          <div className="flex space-x-2">
            <button className="text-blue-400 hover:text-blue-300 p-2">
              <Edit size={20} />
            </button>
            <button className="text-red-400 hover:text-red-300 p-2">
              <Trash2 size={20} />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

function ProfileManagement() {
  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">个人信息管理</h2>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">姓名</label>
          <input
            type="text"
            defaultValue="马君"
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">个人简介</label>
          <textarea
            rows={4}
            defaultValue="音乐创作者 · 视频制作人"
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
          />
        </div>
        
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
          保存更改
        </button>
      </div>
    </div>
  )
}
