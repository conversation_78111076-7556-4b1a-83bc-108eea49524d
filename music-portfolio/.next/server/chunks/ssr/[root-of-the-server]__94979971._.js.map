{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\nimport { Play, Music, Video } from \"lucide-react\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"relative h-screen flex items-center justify-center bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900\">\n        <div className=\"absolute inset-0 bg-black/50\"></div>\n        <div className=\"relative z-10 text-center max-w-4xl mx-auto px-4\">\n          <div className=\"mb-8\">\n            <Image\n              src=\"/avatar.jpg\"\n              alt=\"马君\"\n              width={200}\n              height={200}\n              className=\"rounded-full mx-auto mb-6 border-4 border-white/20\"\n              priority\n            />\n          </div>\n          <h1 className=\"text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent\">\n            马君\n          </h1>\n          <p className=\"text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed\">\n            音乐创作者 · 视频制作人\n          </p>\n          <p className=\"text-lg text-gray-400 mb-12 max-w-2xl mx-auto\">\n            用音乐表达情感，用视频记录生活。在这里分享我的原创音乐作品和精彩视频内容。\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/music\"\n              className=\"inline-flex items-center px-8 py-4 bg-white text-black font-semibold rounded-full hover:bg-gray-200 transition-colors duration-200\"\n            >\n              <Music className=\"mr-2\" size={20} />\n              探索音乐\n            </Link>\n            <Link\n              href=\"/video\"\n              className=\"inline-flex items-center px-8 py-4 border-2 border-white text-white font-semibold rounded-full hover:bg-white hover:text-black transition-colors duration-200\"\n            >\n              <Video className=\"mr-2\" size={20} />\n              观看视频\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Content */}\n      <section className=\"py-20 bg-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <h2 className=\"text-4xl font-bold text-center mb-16\">精选作品</h2>\n\n          <div className=\"grid md:grid-cols-2 gap-12\">\n            {/* Latest Music */}\n            <div className=\"bg-gray-900 rounded-lg p-8 hover:bg-gray-700 transition-colors duration-200\">\n              <div className=\"flex items-center mb-6\">\n                <Music className=\"mr-3 text-blue-400\" size={24} />\n                <h3 className=\"text-2xl font-semibold\">最新音乐</h3>\n              </div>\n              <p className=\"text-gray-400 mb-6\">\n                聆听我最新创作的音乐作品，感受音乐的魅力与情感的表达。\n              </p>\n              <Link\n                href=\"/music\"\n                className=\"inline-flex items-center text-blue-400 hover:text-blue-300 font-medium\"\n              >\n                <Play className=\"mr-2\" size={16} />\n                立即播放\n              </Link>\n            </div>\n\n            {/* Latest Video */}\n            <div className=\"bg-gray-900 rounded-lg p-8 hover:bg-gray-700 transition-colors duration-200\">\n              <div className=\"flex items-center mb-6\">\n                <Video className=\"mr-3 text-red-400\" size={24} />\n                <h3 className=\"text-2xl font-semibold\">最新视频</h3>\n              </div>\n              <p className=\"text-gray-400 mb-6\">\n                观看我在B站发布的最新视频内容，包括音乐创作过程和生活分享。\n              </p>\n              <Link\n                href=\"/video\"\n                className=\"inline-flex items-center text-red-400 hover:text-red-300 font-medium\"\n              >\n                <Play className=\"mr-2\" size={16} />\n                立即观看\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;oCACV,QAAQ;;;;;;;;;;;0CAGZ,8OAAC;gCAAG,WAAU;0CAA4G;;;;;;0CAG1H,8OAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAG7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAAM;;;;;;;kDAGtC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAErD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAqB,MAAM;;;;;;8DAC5C,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAoB,MAAM;;;;;;8DAC3C,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}]}