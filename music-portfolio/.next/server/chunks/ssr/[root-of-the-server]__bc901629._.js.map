{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useState } from 'react'\nimport { Menu, X } from 'lucide-react'\n\nconst Navigation = () => {\n  const pathname = usePathname()\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  const navItems = [\n    { href: '/', label: 'HOME', key: 'home' },\n    { href: '/music', label: 'MUSIC', key: 'music' },\n    { href: '/video', label: 'VIDEOS', key: 'video' },\n  ]\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <nav className=\"bg-black fixed w-full z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"text-white text-2xl font-bold tracking-wider\">\n            <span className=\"font-serif italic\">KIMAHALA</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.key}\n                href={item.href}\n                className={`px-3 py-2 text-sm font-semibold tracking-wider transition-colors duration-200 ${\n                  isActive(item.href)\n                    ? 'text-white'\n                    : 'text-red-400 hover:text-white'\n                }`}\n              >\n                {item.label}\n              </Link>\n            ))}\n\n            {/* Social Icons */}\n            <div className=\"flex items-center space-x-3 ml-6\">\n              <a\n                href=\"https://space.bilibili.com/27744192?spm_id_from=333.1007.0.0\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-red-400 hover:text-white transition-colors duration-200\"\n                title=\"B站主页\"\n              >\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M17.813 4.653h.854c1.51.054 2.769.578 3.773 1.574 1.004.995 1.524 2.249 1.56 3.76v7.36c-.036 1.51-.556 2.769-1.56 3.773s-2.262 1.524-3.773 1.56H5.333c-1.51-.036-2.769-.556-3.773-1.56S.036 18.858 0 17.347v-7.36c.036-1.511.556-2.765 1.56-3.76 1.004-.996 2.262-1.52 3.773-1.574h.774l-1.174-1.12a1.234 1.234 0 0 1-.373-.906c0-.356.124-.658.373-.907l.027-.027c.267-.249.573-.373.92-.373.347 0 .653.124.92.373L9.653 4.44c.071.071.134.142.187.213h4.267a.836.836 0 0 1 .16-.213l2.853-2.747c.267-.249.573-.373.92-.373.347 0 .662.151.929.4.267.249.391.551.391.907 0 .356-.124.657-.373.906l-1.174 1.12zM5.333 7.24c-.746.018-1.373.276-1.88.773-.506.498-.769 1.13-.789 1.894v7.52c.02.764.283 1.396.789 1.894.507.498 1.134.756 1.88.773h13.334c.746-.017 1.373-.275 1.88-.773.506-.498.769-1.13.789-1.894v-7.52c-.02-.765-.283-1.396-.789-1.894-.507-.497-1.134-.755-1.88-.773H5.333zM8 11.107c.373 0 .684.124.933.373.25.249.383.569.4.96v1.173c-.017.391-.15.711-.4.96-.249.249-.56.373-.933.373s-.684-.124-.933-.373c-.25-.249-.383-.569-.4-.96V12.44c.017-.391.15-.711.4-.96.249-.249.56-.373.933-.373zm8 0c.373 0 .684.124.933.373.25.249.383.569.4.96v1.173c-.017.391-.15.711-.4.96-.249.249-.56.373-.933.373s-.684-.124-.933-.373c-.25-.249-.383-.569-.4-.96V12.44c.017-.391.15-.711.4-.96.249-.249.56-.373.933-.373z\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center space-x-3\">\n            <a\n              href=\"https://space.bilibili.com/27744192?spm_id_from=333.1007.0.0\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-red-400 hover:text-white transition-colors duration-200\"\n              title=\"B站主页\"\n            >\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M17.813 4.653h.854c1.51.054 2.769.578 3.773 1.574 1.004.995 1.524 2.249 1.56 3.76v7.36c-.036 1.51-.556 2.769-1.56 3.773s-2.262 1.524-3.773 1.56H5.333c-1.51-.036-2.769-.556-3.773-1.56S.036 18.858 0 17.347v-7.36c.036-1.511.556-2.765 1.56-3.76 1.004-.996 2.262-1.52 3.773-1.574h.774l-1.174-1.12a1.234 1.234 0 0 1-.373-.906c0-.356.124-.658.373-.907l.027-.027c.267-.249.573-.373.92-.373.347 0 .653.124.92.373L9.653 4.44c.071.071.134.142.187.213h4.267a.836.836 0 0 1 .16-.213l2.853-2.747c.267-.249.573-.373.92-.373.347 0 .662.151.929.4.267.249.391.551.391.907 0 .356-.124.657-.373.906l-1.174 1.12zM5.333 7.24c-.746.018-1.373.276-1.88.773-.506.498-.769 1.13-.789 1.894v7.52c.02.764.283 1.396.789 1.894.507.498 1.134.756 1.88.773h13.334c.746-.017 1.373-.275 1.88-.773.506-.498.769-1.13.789-1.894v-7.52c-.02-.765-.283-1.396-.789-1.894-.507-.497-1.134-.755-1.88-.773H5.333zM8 11.107c.373 0 .684.124.933.373.25.249.383.569.4.96v1.173c-.017.391-.15.711-.4.96-.249.249-.56.373-.933.373s-.684-.124-.933-.373c-.25-.249-.383-.569-.4-.96V12.44c.017-.391.15-.711.4-.96.249-.249.56-.373.933-.373zm8 0c.373 0 .684.124.933.373.25.249.383.569.4.96v1.173c-.017.391-.15.711-.4.96-.249.249-.56.373-.933.373s-.684-.124-.933-.373c-.25-.249-.383-.569-.4-.96V12.44c.017-.391.15-.711.4-.96.249-.249.56-.373.933-.373z\"/>\n              </svg>\n            </a>\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-300 hover:text-white p-2\"\n            >\n              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 bg-black\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.key}\n                  href={item.href}\n                  className={`block px-3 py-2 text-base font-semibold tracking-wider transition-colors duration-200 ${\n                    isActive(item.href)\n                      ? 'text-white bg-gray-800'\n                      : 'text-red-400 hover:text-white hover:bg-gray-700'\n                  }`}\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,aAAa;IACjB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAQ,KAAK;QAAO;QACxC;YAAE,MAAM;YAAU,OAAO;YAAS,KAAK;QAAQ;QAC/C;YAAE,MAAM;YAAU,OAAO;YAAU,KAAK;QAAQ;KACjD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;sCAItC,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,8EAA8E,EACxF,SAAS,KAAK,IAAI,IACd,eACA,iCACJ;kDAED,KAAK,KAAK;uCARN,KAAK,GAAG;;;;;8CAajB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;sDACnD,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC;wCAAI,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;kDACnD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;iGAAS,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;gBAMjD,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sFAAsF,EAChG,SAAS,KAAK,IAAI,IACd,2BACA,mDACJ;gCACF,SAAS,IAAM,cAAc;0CAE5B,KAAK,KAAK;+BATN,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB/B;uCAEe", "debugId": null}}]}